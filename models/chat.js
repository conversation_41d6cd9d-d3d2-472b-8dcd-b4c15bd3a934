const mongoose = require('mongoose');
const Message = require('./message');
const Unmatched = require('./unmatched');

let sendSocketUpdateForNumYourTurnChats = null; // added to avoid circular dependency

const chatSchema = new mongoose.Schema(
  {
    createdAt: { type: Date, default: Date.now },
    createdBy: { type: String, ref: 'User' },
    users: [{ type: String, ref: 'User' }],
    dndPostFrom: [{ type: String, ref: 'User' }],
    dndMessageFrom: [{ type: String, ref: 'User' }],
    userIdHash: {
      type: String,
      default() {
        return createUserIdHash(this.users)
             || createUserIdHash(this.users.map((x) => x._id));
      },
    },
    lastMessage: { type: mongoose.Schema.Types.ObjectId, ref: 'Message' },
    lastMessageTime: { type: Date, default: Date.now },
    lastMessageReaction:
    {
      type: {
        sender: { type: String, ref: 'User', required: true }, // User who reacted
        firstName: { type: String },
        reaction: { type: String, required: true }, // Emoji or reaction type
        createdAt: { type: Date, default: Date.now }, // Timestamp
      },
      _id: false,
      required: false, // Make the entire lastMessageReaction field optional
    },
    readReceipts: {
      type: Map,
      of: {
        _id: false,
        lastMessageRead: mongoose.Schema.Types.ObjectId,
        numUnreadMessages: { type: Number, default: 0 },
        numSentMessages: { type: Number, default: 0 },
        viewLastSeenExpiration: { type: Date },
        matchIndicator: { type: String },
      },
      default: {},
    },
    perUserState: [
      {
        _id: false,
        userId: { type: String, ref: 'User' },
        yourTurnState: { type: String, enum: ['uncategorized', 'yourTurn', 'concluded', 'messageSent'] },
        unread: { type: Boolean },
      }
    ],
    numMessages: { type: Number, default: 0 },
    pending: { type: Boolean },
    pendingUser: { type: String, ref: 'User' },
    messaged: { type: Boolean },
    replied: { type: Boolean },
    rejected: { type: Boolean },
    firstMessageBy: { type: String, ref: 'User' },
    numMinutesUntilFirstReply: { type: Number },
    initiatedByDM: { type: Boolean },
    initiatedBySuperLike: { type: Boolean },
    instantMatch: { type: Boolean },
    groupChat: { type: Boolean },
    groupChatName: { type: String, trim: true, maxlength: 500 },
    muted: [{ type: String, ref: 'User' }],
    pinned: [{ type: String, ref: 'User' }],
    hidden: [{ type: String, ref: 'User' }],
    bannedUsers: [{ type: String, ref: 'User' }],
    deletedAt: { type: Date },
    automatedChat: { type: Boolean, default: undefined },
    automatedChatState: {
      stage: { type: String },
      supportAdded: { type: Boolean },
      topic: { type: String },
      issue: { type: String },
      subIssue: { type: String },
      trackId: { type: String, ref: 'TrackAutoreponseUsage' },
      rateSupportShown: { type: Boolean, default: undefined },
      rateSupportShownV2: { type: Boolean, default: undefined },
    },
    meetUp: { type: Boolean, default: undefined },
    contactExchange: { type: Boolean, default: undefined },
    transactions: { type: Boolean, default: undefined },
    chatAnalyzed: { type: Boolean, default: undefined },
    hasMediaFiles: { type: Boolean, default: undefined },
  },
  {
    versionKey: false,
  },
);

chatSchema.index(
  { deletedAt: 1 },
  {
    partialFilterExpression: { deletedAt: { $exists: true } },
  },
);

chatSchema.index({
  userIdHash: 1,
});

// legacy index
chatSchema.index({
  users: 1,
  pendingUser: 1,
  lastMessageTime: -1,
});

// index for received likes
chatSchema.index(
  { pendingUser: 1, lastMessageTime: -1 },
  { partialFilterExpression: { pending: true } },
);
chatSchema.index(
  { initiatedBySuperLike: 1, pendingUser: 1, lastMessageTime: -1 },
  { partialFilterExpression: { initiatedBySuperLike: true, pending: true } },
);

// index for sent likes
chatSchema.index(
  { createdBy: 1, lastMessageTime: -1 },
  { partialFilterExpression: { pending: true } },
);

// index for matches and messages
chatSchema.index(
  { users: 1, messaged: 1, lastMessageTime: -1 },
  { partialFilterExpression: { pendingUser: null } },
);

// index for all contacts
chatSchema.index(
  { users: 1, createdAt: 1 },
  { partialFilterExpression: { pendingUser: null } },
);

chatSchema.index({
  pinned: 1,
});

chatSchema.index(
  {
    'perUserState.userId': 1,
    'perUserState.yourTurnState': 1,
    lastMessageTime: -1,
  },
  { partialFilterExpression: { messaged: true } },
);

chatSchema.index(
  {
    'perUserState.userId': 1,
    'perUserState.unread': 1,
    lastMessageTime: -1,
  },
  { partialFilterExpression: { messaged: true } },
);

chatSchema.index(
  { hidden: 1 },
  { partialFilterExpression: { 'hidden.0': { $exists: true } } },
);

// Define methods
// =============================================================================

chatSchema.pre('save', function (next) {
  if (this.pendingUser === undefined) {
    return next();
  }
  this.pending = this.pendingUser != null;
  return next();
});

function createUserIdHash(ids) {
  if (ids.some((x) => typeof x !== 'string' || !x)) {
    return undefined;
  }
  return [...ids].sort().join('-');
}

chatSchema.methods.incrementNumMessages = function () {
  const chat = this;
  if (!chat.numMessages) {
    chat.numMessages = 0;
  }
  chat.numMessages += 1;
};

chatSchema.statics.createUserIdHash = createUserIdHash;

chatSchema.statics.findDirectChat = async function (userId1, userId2, populate, includeDeleted) {
  const query = {
    userIdHash: createUserIdHash([userId1, userId2]),
    groupChat: { $ne: true },
    deletedAt: null,
  };
  if (includeDeleted) {
    delete query.deletedAt;
  }
  if (populate) {
    return await this
      .findOne(query)
      .populate('users')
      .populate('lastMessage');
  }

  return await this
    .findOne(query);
};

chatSchema.statics.removeFromGroupChat = async function (chatId, userId, msg) {
  const result = await this.updateOne(
    { _id: chatId, groupChat: true, users: userId },
    { $pull: { users: userId, muted: userId } },
  );
  if (!result.modifiedCount) {
    return;
  }
  const chat = await this.findOne({ _id: chatId });
  if (chat.users.length == 0) {
    await this.removeChatAndMessages(chat);
    return;
  }

  const message = new Message({
    createdAt: Date.now(),
    chat: chatId,
    text: msg,
    sender: '',
    systemMessage: true,
  });
  await message.save();
  chat.lastMessage = message;
  chat.lastMessageTime = Date.now();
  await chat.save();

  return message;
};

chatSchema.statics.setSendSocketUpdateForNumYourTurnChats = function (fn) {
  sendSocketUpdateForNumYourTurnChats = fn;
};

chatSchema.statics.removeChatAndMessages = async function (chat) {
  if(!chat || chat.deletedAt) return
  await this.updateOne(
    { _id: chat._id },
    { deletedAt: Date.now() },
  );

  await Message.updateMany(
    { chat: chat._id },
    { deletedAt: Date.now() },
  );

  // decreasing yourTurnState for deleting chats
  if (Array.isArray(chat.perUserState) && !chat.bannedUsers?.length) {
    for (const state of chat.perUserState) {
      if (['uncategorized', 'yourTurn'].includes(state.yourTurnState)) {
        await mongoose.model('User').incrementMetric(state.userId, 'numYourTurnChats', -1);
        if(sendSocketUpdateForNumYourTurnChats){
          await sendSocketUpdateForNumYourTurnChats(state.userId)
        }
      }
    }
  }

  if (!chat.pendingUser && !chat.groupChat && !chat.automatedChat) {
    const [userA, userB] = await mongoose.model('User').find({ _id: { $in: chat.users } }).select('firstName');

    const records = [
      {
        user: userA._id.toString(),
        otherUser: userB._id.toString(),
        otherUserName: userB.firstName || '',
        lastMessageTime: chat.lastMessageTime,
      },
      {
        user: userB._id.toString(),
        otherUser: userA._id.toString(),
        otherUserName: userA.firstName || '',
        lastMessageTime: chat.lastMessageTime,
      },
    ];

    await Unmatched.insertMany(records);
  }
};

// Export schema =====================================================================================================================================================================
module.exports = mongoose.model('Chat', chatSchema);
