const mailchimp = require('./mailchimp-transactional');
const { WEB_DOMAIN, IMAGE_DOMAIN } = require('./constants');
const socialLib = require('./social');
const path = require('path');
const { I18n } = require('i18n');
const { locales, translate, translate_frontend } = require('./translate');
const User = require('../models/user');
const ses = require('../lib/ses');
const emailUnsubLib = require('../lib/email-unsub');
const moment = require('moment');
const { readPreference, replicaTags } = require('../lib/read-preference-analytics');

// i18n instance specifically for emails
const i18n_emails = new I18n({
  directory: path.join(__dirname, 'locales-emails'),
  updateFiles: !!process.env.UPDATE_EMAIL_TRANSLATION_FILES,
  indent: '  ',
});

const getTranslatedTime = (timeZone) => {
  const options = {
    year: 'numeric',
    month: 'long',
    day: '2-digit',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
    timeZone: timeZone || 'Europe/London',
  };

  const formatter = new Intl.DateTimeFormat('en', options);
  return formatter.format(new Date());
};

function validateEmailOptions(emailOptions) {
  // Define required keys and their expected types
  const requiredKeys = ['templateName', 'email', 'locale', 'timezone', 'subject', 'greetings', 'introText', 'signInText', 'ignoreText', 'signInLink', 'V5_LOGIN_INTRO', 'V5_MOTTO', 'COPYRIGHT', 'V5_ADDRESS'];

  // Check if all required keys are present and are strings
  for (const key of requiredKeys) {
    if (!(key in emailOptions)) {
      throw new Error(`Missing required key in emailOptions: ${key}`);
    }
    if (typeof emailOptions[key] !== 'string') {
      throw new Error(`Invalid type for ${key} in emailOptions. Expected string, got ${typeof emailOptions[key]}`);
    }
  }
}

function translateEmail(obj, ...subs) {
  let phrase = obj.phrase;
  let locale = obj.locale;
  if (!phrase) {
    return phrase;
  }
  if (!locale || !locales.includes(locale)) {
    locale = 'en';
  }
  return i18n_emails.__({ phrase, locale }, ...subs);
}

function prepareTags({ name, phrase, locale, variables, shouldTranslate = true }) {
  const hasValidVariables = variables && typeof variables === 'object' && Object.keys(variables).length > 0;

  const content = shouldTranslate
    ? hasValidVariables
      ? translateEmail({ phrase, locale }, variables)
      : translateEmail({ phrase, locale })
    : phrase;

  return { name, content };
}

const EMAIL_BATCH_SIZE = 50;

// required fields in user: [_id, locale, firstName, email, pushNotificationSettings.email, pictures]
async function sendEmailTemplate(user, templateName, originalSubject, additionalMergeTags = []) {
  const settings = user.pushNotificationSettings;
  if (!user || !user.email || (settings && settings.email === false)) {
    return;
  }

  const locale = user.locale;
  const subject = translateEmail({phrase: originalSubject, locale: locale}, { name: user.firstName });
  const englishSubject = translateEmail({phrase: originalSubject, locale: 'en'}, { name: user.firstName });

  if (locale != null && locale != 'en' && englishSubject == subject) {
    // console.log(templateName, 'not translated', locale);
    return;
  }

  const mergeTags = additionalMergeTags.concat([
    {
      name: 'FNAME',
      content: user.firstName,
    },
    {
      name: 'USER_IMAGE',
      content: IMAGE_DOMAIN + user.pictures[0],
    },
  ]);
  const response = await mailchimp.messages.sendTemplate({
    template_name: templateName,
    template_content: [],
    message: {
      to: [
        {
          email: user.email,
        },
      ],
      global_merge_vars: mergeTags,
      inline_css: true,
      from_email: '<EMAIL>',
      from_name: 'Boo',
      subject,
    },
  });
  // console.log(`Sent email to ${user._id} ${user.email}, mailchimp response: ${JSON.stringify(response)}`);
  return response;
}

async function sendEmailTemplateSES(user, templateName, originalSubject, additionalMergeTags = []) {
  const settings = user.pushNotificationSettings;
  if (!user || !user.email || (settings && settings.email === false)) {
    return;
  }
  if (user.deletionRequestDate && !templateName.includes('delete-account')) {
    return;
  }

  const locale = user.locale;
  const subject = translateEmail({phrase: originalSubject, locale: locale}, { name: user.firstName });
  const englishSubject = translateEmail({phrase: originalSubject, locale: 'en'}, { name: user.firstName });

  if (locale != null && locale != 'en' && englishSubject == subject) {
    // console.log(templateName, 'not translated', locale);
    return;
  }

  const templateData = {
    SUBJECT: subject,
    FNAME: user.firstName,
    UNSUBSCRIBE_LINK: getUnsubLink(user),
    USER_IMAGE: IMAGE_DOMAIN + user.pictures[0],
  };
  for (const mergeTag of additionalMergeTags) {
    templateData[mergeTag.name] = mergeTag.content;
  }

  try {
    const params = {
      Destination: {
        ToAddresses: [
          user.email,
        ]
      },
      Source: 'Boo <<EMAIL>>',
      Template: templateName,
      TemplateData: JSON.stringify(templateData),
      ConfigurationSetName: 'default',
      Tags: [
        {
          Name: 'template',
          Value: templateName,
        },
      ],
    };
    // console.log(params);
    const response = await ses.sendTemplatedEmail(params).promise();
    // console.log(response);
  } catch (err) {
    console.log(err);
  }
}

async function sendSignInEmailSES(emailOptions) {
  const { templateName, email, locale, timezone, signInLink } = emailOptions;

  const time = getTranslatedTime(timezone);
  const subject = translateEmail(
    { phrase: 'Sign in to Boo requested at {{time}}', locale },
    { time },
  );

  const templateData = {
    subject,
    SUBJECT: subject,
    V5_LOGIN_INTRO: translateEmail({ phrase: `Here's Your Boo Login Link`, locale }),
    greetings: translateEmail({ phrase: 'Hello,', locale }),
    introText: translateEmail(
      { phrase: 'We received a request to sign in to Boo using this email address, at {{time}}. If you want to sign in with your {{email}} account, click this link:', locale },
      { time, email },
    ),
    signInLink,
    signInText: translateEmail({ phrase: 'Sign in to Boo', locale }).toUpperCase(),
    ignoreText: translateEmail({ phrase: 'If you did not request this link, you can safely ignore this email.', locale }),
    V5_MOTTO: translateEmail({ phrase: `We stand for love.`, locale }),
    COPYRIGHT: translateEmail(
      { phrase: 'Copyright © {{year}} Boo, all rights reserved.', locale },
      { year: moment().year() },
    ),
    V5_ADDRESS: '525 3rd St Lake Oswego, Oregon 97034, USA',
    DIR: ['ar', 'fa', 'he', 'ps', 'sd', 'ug', 'ur', 'yi'].includes(locale) ? 'rtl' : 'ltr',
  };
  validateEmailOptions({ ...templateData, ...emailOptions });

  try {
    const params = {
      Destination: {
        ToAddresses: [email],
      },
      Source: 'Boo <<EMAIL>>',
      Template: templateName,
      TemplateData: JSON.stringify(templateData),
      ConfigurationSetName: 'no-click-tracking',
      Tags: [
        {
          Name: 'template',
          Value: templateName,
        },
      ],
    };

    const response = await ses.sendTemplatedEmail(params).promise();
    // console.log(response);
  } catch (err) {
    console.log(err);
  }
}

async function sendEmailForNewSitemapIndexSES(newSitemapIndex) {
  const emailContent = `<html><body><p>Hello SEO Team,</p><p>New Sitemap Indexes have been created:</p><ul>${newSitemapIndex.map(url => `<li><a href="${url}" target="_blank">${url}</a></li>`).join('')}</ul><p>Regards,</p><p>Boo Technical Team</p></body></html>`;
  // console.log('Email content for the new Sitemap Index Created', emailContent)
  const params = {
    Destination: {
      ToAddresses: ['<EMAIL>','<EMAIL>'],
    },
    Message: {
      Body: {
        Html: {
          Charset: 'UTF-8',
          Data: emailContent,
        },
      },
      Subject: {
        Charset: 'UTF-8',
        Data: 'New Sitemap Index Created',
      },
    },
    Source: 'Boo <<EMAIL>>',
  };

  try {
    const result = await ses.sendEmail(params).promise();
    // console.log('Email sent successfully for new sitemap index created:', result);
  } catch (error) {
    console.error('Error sending email for new sitemap index created:', error);
  }
}

async function sendGenericEmailAlert(recipients, subject, emailContent) {
  const params = {
    Destination: {
      ToAddresses: recipients,
    },
    Message: {
      Body: {
        Html: {
          Charset: 'UTF-8',
          Data: emailContent,
        },
      },
      Subject: {
        Charset: 'UTF-8',
        Data: subject,
      },
    },
    Source: 'Boo <<EMAIL>>',
  };

  try {
    const result = await ses.sendEmail(params).promise();
  } catch (error) {
    console.error('sendGenericEmailAlert: Error sending:', error);
  }
}

async function sendAlertForBanAttemptOnBanExemptUser(emailContent) {
  await sendGenericEmailAlert(
    ['<EMAIL>','<EMAIL>'],
    `[${process.env.NODE_ENV}] Ban Attempt On Ban Exempt User`,
    emailContent,
  );
}

async function sendEmailNotifyingAboutBannedUser(email, userName, bannedUserName) {
  const emailContent = `Hi ${userName},

This email is notify you that one of your matches, ${bannedUserName}, has been banned for fraudulent behavior. Please exercise caution when exchanging information, and please review our Safety Tips.

Love,
Boo`;
  const params = {
    Destination: {
      ToAddresses: [email],
    },
    Message: {
      Body: {
        Text: {
          Charset: 'UTF-8',
          Data: emailContent,
        },
      },
      Subject: {
        Charset: 'UTF-8',
        Data: 'Safety Alert',
      },
    },
    Source: 'Boo <<EMAIL>>',
  };

  try {
    const result = await ses.sendEmail(params).promise();
  } catch (error) {
    console.error('Error sending email notifying about banned user:', error);
  }
}

async function getGlobalMergeVars(locale) {

  // get top posts in feed
  const posts = await socialLib.getQuestionFeed({
    sort: 'popular',
    language: locale,
    additionalMatchByFilters: {
      audio: null,
      poll: null,
    },
  });
  if (posts.length < 4) {
    return [];
  }

  let globalMergeVars = [
    ...getCommonTranslatedTags({locale, firstName: ''}),
  ];

  for (let i = 0; i < 4; i++) {
    const post = posts[i];
    if (post) {
      globalMergeVars = globalMergeVars.concat([
        {
          name: `POST${i}_TAG`,
          content: `#${post.interestName}`,
        },
        {
          name: `POST${i}_TITLE`,
          content: i == 0 ? translate('Question of the Day', locale) : post.title,
        },
        {
          name: `POST${i}_DESCRIPTION`,
          content: post.text,
        },
        {
          name: `POST${i}_DATE`,
          content: translate_frontend('%sd', locale, '1'),
        },
        {
          name: `POST${i}_NUM_LIKES`,
          content: post.numLikes,
        },
        {
          name: `POST${i}_NUM_COMMENTS`,
          content: post.numComments,
        },
        {
          name: `POST${i}_URL`,
          content: post.url,
        },
      ]);

      const { profilePreview } = post;
      if (profilePreview) {
        const userPicture = `https://images.${process.env.NODE_ENV}.boo.dating/${profilePreview.picture}?d=100x100`;
        globalMergeVars = globalMergeVars.concat([
          {
            name: `POST${i}_USER_NAME`,
            content: profilePreview.firstName,
          },
          {
            name: `POST${i}_USER_MBTI`,
            content: profilePreview.personality?.mbti,
          },
          {
            name: `POST${i}_USER_HOROSCOPE`,
            content: translate_frontend(profilePreview.horoscope, locale),
          },
          {
            name: `POST${i}_USER_PICTURE`,
            content: userPicture,
          },
        ]);
      }
    }
  }

  return globalMergeVars;
}

async function sendDailyDigestEmailHelper(users, globalMergeVars, locale, templateName) {

  if (!users.length) {
    return;
  }

  const destinations = users.map((user) => (
    {
      Destination: { ToAddresses: [user.email] },
      ReplacementTemplateData: JSON.stringify({
        FNAME: user.firstName,
        SUBJECT: translateEmail({phrase: "{{name}}, here's what you missed ✨", locale: locale}, { name: user.firstName }),
        YOU_ARE_RECEIVING_THIS_EMAIL_BECAUSE: getUnsubText(user),
        UNSUBSCRIBE_LINK: getUnsubLink(user),
      }),
    }
  ));
  const templateData = {};
  for (const mergeTag of globalMergeVars) {
    templateData[mergeTag.name] = mergeTag.content;
  }

  Object.assign(templateData, {
    V5_DIGEST_INTRO: translateEmail({ phrase: `Check Out the Latest Posts on Boo!`, locale }),
    V5_DIGEST_TIP: translateEmail({ phrase: `Join the conversation now in the Boo app!`, locale }),
  });

  try {
    const params = {
      Destinations: destinations,
      Source: 'Boo <<EMAIL>>',
      Template: templateName,
      DefaultTemplateData: JSON.stringify(templateData),
      ConfigurationSetName: 'default',
      DefaultTags: [
        {
          Name: 'template',
          Value: templateName,
        },
      ],
    };
    // console.log(params);
    const response = await ses.sendBulkTemplatedEmail(params).promise();
    // console.log(response);
  } catch (err) {
    console.log(err);
  }
}

async function sendDailyDigestEmail(allUsers, globalMergeVars, locale) {
  const templateName = 'daily-digest-v6';
  await sendDailyDigestEmailHelper(allUsers, globalMergeVars, locale, templateName);
}

async function sendDailyDigestToCursor(query, projection) {

  for (const locale of locales) {

    const originalSubject = '{{name}}, here\'s what you missed ✨';
    const subject = translateEmail({phrase: originalSubject, locale: locale});
    const englishSubject = translateEmail({phrase: originalSubject, locale: 'en'});

    if (locale != null && locale != 'en' && englishSubject == subject) {
      // console.log('daily digest not translated for', locale);
      continue;
    }

    const globalMergeVars = await getGlobalMergeVars(locale);
    if (!globalMergeVars.length) {
      continue;
    }
    const usersCursor = User.find({ ...query, locale}, projection)
                            .read(readPreference, replicaTags)
    let users = [];
    let count = 0;
    for await (const doc of usersCursor) {
      if (doc) {
        users.push(doc);
        if (users.length == EMAIL_BATCH_SIZE) {
          /// /send email to users if batch size reached
          await sendDailyDigestEmail(users, globalMergeVars, locale);
          users = [];
        }
        count++;
      }
    }

    if (users.length > 0) {
      // send email to left users
      await sendDailyDigestEmail(users, globalMergeVars, locale);
    }
    // console.log(`Sent daily digest mail to ${count} users, locale: ${locale}`);
  }
}

function getUnsubLink(user) {
  return `${WEB_DOMAIN}/unsubscribe?email=${encodeURIComponent(user.email)}&hash=${emailUnsubLib.unsubHash(user._id, user.email)}`;
}

function getUnsubText(user) {
  return `${translateEmail({ phrase: 'You are receiving this email because you opted in via the Boo mobile app or website.', locale: user.locale })} <a href="${getUnsubLink(user)}">${translateEmail({ phrase: 'Unsubscribe', locale: user.locale })}</a>`;
}

function getCommonTranslatedTags(user) {
  const baseTags = [
    {
      name: 'HI_NAME',
      content: translateEmail({ phrase: 'Hi {{name}},', locale: user.locale }, { name: user.firstName }),
    },
    {
      name: 'MEET_NEW_PEOPLE',
      content: translateEmail({ phrase: 'Meet New People', locale: user.locale }),
    },
    {
      name: 'OPEN_BOO',
      content: translateEmail({ phrase: 'OPEN BOO', locale: user.locale }),
    },
    {
      name: 'SEE_WHO',
      content: translateEmail({ phrase: 'SEE WHO', locale: user.locale }),
    },
    {
      name: 'COPYRIGHT',
      content: translateEmail({ phrase: 'Copyright © {{year}} Boo, all rights reserved.', locale: user.locale }, { year: moment().year() }),
    },
    {
      name: 'YOU_ARE_RECEIVING_THIS_EMAIL_BECAUSE',
      content: getUnsubText(user),
    },
    {
      name: 'ADDRESS',
      content: translateEmail({ phrase: '1050 Queen Street, Suite 100, Honolulu, HI 96815, USA', locale: user.locale }),
    },
  ];

  const v5Tags = [
    {
      name: 'V5_MOTTO',
      content: translateEmail({ phrase: 'We stand for love.', locale: user.locale }),
    },
    {
      name: 'V5_ADDRESS',
      content: '525 3rd St Lake Oswego, Oregon 97034, USA',
    },
    {
      name: 'V5_UNSUB_TEXT',
      content: translateEmail({ phrase: 'You are receiving this email because you have an account with Boo.', locale: user.locale }),
    },
    {
      name: 'V5_UNSUB_BTN',
      content: translateEmail({ phrase: 'Unsubscribe', locale: user.locale }),
    },
    {
      name: 'V5_OPEN_APP_TEXT',
      content: translateEmail({ phrase: `Open the app now and find out who's interested in you!`, locale: user.locale }),
    },
    {
      name: 'V5_HAPPY_MATCHING',
      content: translateEmail({ phrase: 'Happy matching,', locale: user.locale }),
    },
    {
      name: 'DIR',
      content: ['ar', 'fa', 'he', 'ps', 'sd', 'ug', 'ur', 'yi'].includes(user.locale) ? 'rtl' : 'ltr',
    },
  ];

  return [...baseTags, ...v5Tags];
}

async function sendFlashSaleEmail(user) {
  let template = 'flash-sale-v6';

  const basePhrases = {
    CLAIM_NOW: `CLAIM NOW`,
    V5_SALE_INTRO: `Hi {{name}}, Get 50% Off Now! 🎁`,
    V5_SALE_INFO: `Exciting news! We're giving you an exclusive 50% off for a limited time. Don't miss your chance to unlock premium features and connect with more amazing souls!`,
    V5_SALE_TIP: `Hurry—this offer won't last long!`,
  };

  const baseVariables = {
    V5_SALE_INTRO: { name: user.firstName },
  };

  const dynamicTags = Object.entries(basePhrases).map(([name, phrase]) =>
    prepareTags({ name, phrase, locale: user.locale, variables: baseVariables[name] }),
  );

  await sendEmailTemplateSES(
    user,
    template,
    '{{name}}, get 50% off Boo Infinity! ✨',
    [
      ...getCommonTranslatedTags(user),
      ...dynamicTags,
    ],
  );
}

async function sendDeleteAccountEmail(user) {
  let templateName = 'delete-account-v6';

  const basePhrases = {
    CANCEL_DELETION: `CANCEL DELETION`,
    LOVE: `Love,`,
    THE_SOULS_AT_BOO: `The souls at Boo`,
    V5_DELETE_INTRO: `We're Sad to See You Go, {{name}}`,
    V5_DELETE_INFO: `Your account is scheduled for permanent deletion. If this was a mistake or you've changed your mind, you still have time to cancel.`,
    V5_DELETE_THANKS: `Thank you for being a part of our community.`,
    V5_DELETE_TIP: `If you proceed, all your data will be permanently removed and cannot be recovered.`,
  };

  const baseVariables = {
    V5_DELETE_INTRO: { name: user.firstName },
  };

  const dynamicTags = Object.entries(basePhrases).map(([name, phrase]) =>
    prepareTags({ name, phrase, locale: user.locale, variables: baseVariables[name] }),
  );

  const res = await sendEmailTemplateSES(
    user,
    templateName,
    '{{name}}, your account is about to be deleted',
    [
      ...getCommonTranslatedTags(user),
      ...dynamicTags,
    ],
  );
  // console.log(`25-day deleted account email: ${user._id} ${user.email} ${res}`);
}

async function sendInactiveEmail(user) {
  let templateName = 'inactive-v6';

  const basePhrases = {
    YOUR_PROFILE_HAS_BEEN_INACTIVE: `Your profile has been inactive for a while and is about to be shown less to other souls.`,
    OPEN_THE_APP_AGAIN: `Open the app again to be shown to more people!`,
    BOOST_PROFILE: `BOOST PROFILE`,
    V5_INACTIVE_INTRO: `Your Profile Has Been Inactive 😢`,
  };

  const dynamicTags = Object.entries(basePhrases).map(([name, phrase]) =>
    prepareTags({ name, phrase, locale: user.locale }),
  );

  const res = await sendEmailTemplateSES(
    user,
    templateName,
    "{{name}}, don't lose your spot ✨",
    [
      ...getCommonTranslatedTags(user),
      ...dynamicTags,
    ],
  );
  // console.log(`7-day inactive email: ${user._id} ${user.email} ${res}`);
}

async function sendNewLikeEmail(user, templateOverride) {
  const basePhrases = {
    SOMEONE_SENT_YOU_LOVE: 'Someone sent you love. ✨', // required in new-like-v4, new-like-dating-v6
  };

  let templateName;
  let subject = '{{name}}, someone sent you love ✨';

  const isDating = user.preferences?.dating?.length > 0;

  if (templateOverride) {
    templateName = templateOverride;
  } else {
    templateName = isDating ? 'new-like-dating-v6' : 'new-like-friends-v6';
  }

  if (templateName === 'new-like-friends-v6') {
    subject = '{{name}}, someone sent you a friend request ✨';
  }

  if (templateName === 'new-like-dating-v6') {
    Object.assign(basePhrases, {
      V5_DATE_INTRO: `Exciting news! Someone just sent you love—could it be your perfect match? Don't keep them waiting!`,
    });
  } else if (templateName === 'new-like-friends-v6') {
    Object.assign(basePhrases, {
      V5_FRIEND_GREET: `Someone sent you a friend request 🌟`,
      V5_FRIEND_INTRO: `Exciting news! Someone just sent you a friend request.`,
    });
  }

  const dynamicTags = Object.entries(basePhrases).map(([name, phrase]) =>
    prepareTags({ name, phrase, locale: user.locale }),
  );

  const res = await sendEmailTemplateSES(
    user,
    templateName,
    subject,
    [
      ...getCommonTranslatedTags(user),
      ...dynamicTags,
    ],
  );

  // console.log(`New like email: ${user._id} ${user.email} ${res}`);
}

async function sendNewMatchEmail(user) {
  let templateName = 'new-match-v6';

  const basePhrases = {
    V5_MATCH_GREET: `Hi {{name}}, You Matched With Someone! 💕`,
    V5_MATCH_INTRO: `Great news! You've just matched with someone amazing. Now's the time to start a conversation and see where things go!`,
    V5_MATCH_TIP: `Don't wait—open the app and get chatting!`,
  };

  const baseVariables = {
    V5_MATCH_GREET: { name: user.firstName },
  };

  const dynamicTags = Object.entries(basePhrases).map(([name, phrase]) =>
    prepareTags({ name, phrase, locale: user.locale, variables: baseVariables[name] }),
  );

  const res = await sendEmailTemplateSES(
    user,
    templateName,
    '{{name}}, you matched with another soul ✨',
    [
      ...getCommonTranslatedTags(user),
      ...dynamicTags,
    ],
  );
  // console.log(`New match email: ${user._id} ${user.email} ${res}`);
}

async function sendNewMessageEmail(user, partnerName) {
  let templateName = 'new-message-v6';

  const basePhrases = {
    READ_MESSAGE: `READ MESSAGE`,
    V5_MSG_INTRO: `Someone sent you a message 🌟`,
    V5_MSG_INFO: `{{name}} just sent you a message!`,
    V5_MSG_TIP: `Don't keep them waiting—open the app and dive into the conversation!`,
  };

  const baseVariables = {
    V5_MSG_INFO: { name: partnerName },
  };

  const dynamicTags = Object.entries(basePhrases).map(([name, phrase]) =>
    prepareTags({
      name,
      phrase,
      locale: user.locale,
      variables: baseVariables[name],
      translate: name !== 'PARTNER_NAME',
    }),
  );

  const res = await sendEmailTemplateSES(
    user,
    templateName,
    '{{name}}, someone sent you a message ✨',
    [
      ...getCommonTranslatedTags(user),
      ...dynamicTags,
    ],
  );
  // console.log(`New msg email: ${user._id} ${user.email} ${res}`);
}

async function sendNewRecommendationsEmail(user) {
  let templateName = 'new-recommendations-v6';

  const basePhrases = {
    V5_RECOM_INTRO: `Say Hi to Someone New 🌟`,
    V5_RECOM_TIP: `New souls are waiting for you to check them out! Don't miss the chance to connect.`,
  };

  const dynamicTags = Object.entries(basePhrases).map(([name, phrase]) =>
    prepareTags({ name, phrase, locale: user.locale }),
  );

  const res = await sendEmailTemplateSES(
    user,
    templateName,
    '{{name}}, check out these new souls ✨',
    [
      ...getCommonTranslatedTags(user),
      ...dynamicTags,
    ],
  );
  // console.log(`New recs email: ${user._id} ${user.email} ${res}`);
}

async function sendWelcomeEmail(user) {
  let templateName = 'welcome-v6';

  const basePhrases = {
    V5_WLC_GREET: `Welcome to Boo, {{name}}!`,
    V5_WLC_INTRO: `We're so excited to have you here! Here's what you can expect as you start your journey with Boo:`,
    V5_WLC_FEAT_1_TITLE: `Deeply Understand Anyone`,
    V5_WLC_FEAT_1_DESC: `Unlock in-depth personality analysis and dating advice. Your AI wing-ghost is at your service!`,
    V5_WLC_FEAT_2_TITLE: `Compatible, Effortless Chemistry`,
    V5_WLC_FEAT_2_DESC: `We recommend the personalities you're most likely to love and be compatible with.`,
    V5_WLC_FEAT_3_TITLE: `Meet People That Intuitively Understand You`,
    V5_WLC_FEAT_3_DESC: `Enjoy the connections that just click.`,
    V5_WLC_TIP_INTRO: `Follow these helpful tips:`,
    V5_WLC_TIP_1_TITLE: `1. Verify Your Profile`,
    V5_WLC_TIP_1_DESC: `Help us keep Boo safe and honest, and let others know you're real.`,
    V5_WLC_TIP_2_TITLE: `2. Get Maximum Visibility`,
    V5_WLC_TIP_2_DESC: `Open the app every day, upload more pictures, write longer bios, and verify your profile to get shown to more souls.`,
    V5_WLC_TIP_3_TITLE: `3. Upgrade to Boo Infinity`,
    V5_WLC_TIP_3_DESC: `Join 10,000+ other souls who leveled up and receive up to 3X more matches on average.`,
    V5_WLC_LETS_GO_BTN: `OKAY, LET'S GET STARTED!`,
  };

  const baseVariables = {
    V5_WLC_GREET: { name: user.firstName },
  };

  const dynamicTags = Object.entries(basePhrases).map(([name, phrase]) =>
    prepareTags({ name, phrase, locale: user.locale, variables: baseVariables[name] }),
  );

  await sendEmailTemplateSES(
    user,
    templateName,
    '{{name}}, welcome to Boo',
    [
      ...getCommonTranslatedTags(user),
      ...dynamicTags,
    ],
  );
}

async function sendVerificationSuccessfulEmail(user) {
  let template = 'verification-successful-v6';

  const basePhrases = {
    V5_VERIFY_GREET: `🎉 Hi {{name}}, Your Profile is Now Verified!`,
    V5_VERIFY_INTRO: `Great news! Your profile has been successfully verified. You're now ready to connect with more authentic and like minded souls on Boo.`,
    V5_VERIFY_TIP: `Start exploring and making meaningful connections today!`,
  };

  const baseVariables = {
    V5_VERIFY_GREET: { name: user.firstName },
  };

  const dynamicTags = Object.entries(basePhrases).map(([name, phrase]) =>
    prepareTags({ name, phrase, locale: user.locale, variables: baseVariables[name] }),
  );

  await sendEmailTemplateSES(
    user,
    template,
    'Verification Successful',
    [
      ...getCommonTranslatedTags(user),
      ...dynamicTags,
    ],
  );
}

async function sendVerificationFailedEmail(user, reasonBody) {
  let template = 'verification-unsuccessful-v6';

  const basePhrases = {
    V5_VERIFY_FAILED_INTRO: 'Profile Verification Unsuccessful',
    V5_VERIFY_FAILED_INFO: 'It looks like your profile verification was not successful.',
    V5_VERIFY_FAILED_THANKS: 'Thank you for helping us keep Boo safe!',
  };

  const dynamicTags = Object.entries(basePhrases).map(([name, phrase]) =>
    prepareTags({ name, phrase, locale: user.locale }),
  );

  await sendEmailTemplateSES(
    user,
    template,
    'Verification Unsuccessful',
    [
      ...getCommonTranslatedTags(user),
      ...dynamicTags,
    ],
  );
}

module.exports = {
  sendEmailTemplate,
  getGlobalMergeVars,
  sendDailyDigestToCursor,
  sendDailyDigestEmailHelper,
  getCommonTranslatedTags,
  translateEmail,
  sendEmailTemplateSES,
  getUnsubText,
  sendSignInEmailSES,
  getTranslatedTime,
  sendEmailForNewSitemapIndexSES,
  sendGenericEmailAlert,
  sendAlertForBanAttemptOnBanExemptUser,
  sendEmailNotifyingAboutBannedUser,
  sendDeleteAccountEmail,
  sendFlashSaleEmail,
  sendInactiveEmail,
  sendNewLikeEmail,
  sendNewMatchEmail,
  sendNewMessageEmail,
  sendNewRecommendationsEmail,
  sendVerificationSuccessfulEmail,
  sendVerificationFailedEmail,
  sendWelcomeEmail,
  getUnsubLink,
};
