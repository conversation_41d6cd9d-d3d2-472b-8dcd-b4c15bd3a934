const lookup = require('country-code-lookup');

const countryCodes = lookup.countries.map((c) => c.iso2);

let IMMEDIATE_DELETION_ID = 'MOCK_IMMEDIATE_DELETION_ID';
if (process.env.NODE_ENV == 'prod') {
  IMMEDIATE_DELETION_ID = 'ShSu7IYmNFebqGGBZg6VJ2MCeNX2';
}
if (process.env.NODE_ENV == 'beta') {
  IMMEDIATE_DELETION_ID = 'eg32yJMtqWUwDq8yJlLQlPMrG5y2';
}
if (process.env.NODE_ENV == 'dev') {
  IMMEDIATE_DELETION_ID = 'rg0GdfABzSUb7dzUShmt7E30bvp2';
}

const WEB_DOMAIN = process.env.WEB_DOMAIN || 'https://boo.world';
const IMAGE_DOMAIN = process.env.IMAGE_DOMAIN || 'MOCK_IMAGE_DOMAIN/';

const DAILY_PROFILE_LIMIT = parseInt(process.env.DAILY_PROFILE_LIMIT || 30, 10);
const DAILY_USER_WHO_LIKED_PROFILE_LIMIT = process.env.NODE_ENV === 'test' ? 3 : 10;
const DAILY_USER_BOOST_USING_PROFILE_LIMIT = process.env.NODE_ENV === 'test' ? 3 : 10;
const DAILY_LIKES_LIMIT = parseInt(process.env.DAILY_LIKES_LIMIT || 10, 10);
const REPORTS_UNTIL_DELETION = parseInt(process.env.REPORTS_UNTIL_DELETION || 5);
const DEEPGRAM_API_KEY = process.env.DEEPGRAM_API_KEY || 'MOCK_API_KEY';
const YOTI_URL = 'https://api.yoti.com/ai/v1';
const YOTI_LIVENESS_ENDPOINT = '/antispoofing';
const YOTI_PEM_PATH = process.env.YOTI_PEM_PATH || 'MOCK_PEM_PATH';
const YOTI_SDK_ID = process.env.YOTI_SDK_ID || 'MOCK_SDK_ID';

function getDailyProfileLimit() {
  return DAILY_PROFILE_LIMIT;
}

const pageSize = parseInt(process.env.PAGE_SIZE || 20, 10);
function getPageSize() {
  return pageSize;
}

const instantMatchDelayMs = Number(process.env.INSTANT_MATCH_DELAY_MS || 1000);
const instantMatchCooldownMs = Number(process.env.COOLDOWN || 3600000); // 1 hr

const validOs = ['android', 'ios', 'web'];

const KARMA_TIERS = [0, 50, 100, 250, 500, 1000, 2500, 5000, 10000, 25000, 50000];
const KARMA_TIER_SWIPE_LIMITS = [30, 35, 45, 50, 55, 60, 65, 70, 75, 85, 100];
const KARMA_TIER_GHOST_METER = [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60];
const rejectionReasons = [
  'Incorrect pose',
  'No facial photo',
  'Not same person',
  'Photo unclear',
  'Incorrect pose.',
  'Nose challenge failed.',
  'Verification does not match profile pictures.',
  'Poor lighting.',
  'Make sure your first profile picture is a picture of you.',
  'Face challenge failed.',
  'This version of the app is no longer supported. Please update to the latest version of Boo. Thank you!',
  'Make sure your first profile picture is a picture of you, and only you.'
];

const KARMA_CONFIG = [
  {
    level: 0,
    crystal: null,
    karmaTiers: 0,
    karmaTierSwipeLimits: 30,
    coinReward: 0,
  },
  {
    level: 1,
    crystal: 'turquoise',
    karmaTiers: 50,
    karmaTierSwipeLimits: 35,
    coinReward: 50,
  },
  {
    level: 2,
    crystal: 'ruby',
    karmaTiers: 100,
    karmaTierSwipeLimits: 45,
    coinReward: 50,
  },
  {
    level: 3,
    crystal: 'sapphire',
    karmaTiers: 250,
    karmaTierSwipeLimits: 50,
    coinReward: 150,
  },
  {
    level: 4,
    crystal: 'emerald',
    karmaTiers: 500,
    karmaTierSwipeLimits: 55,
    coinReward: 250,
  },
  {
    level: 5,
    crystal: null,
    karmaTiers: 750,
    karmaTierSwipeLimits: 55,
    coinReward: 250,
  },
  {
    level: 6,
    crystal: 'amber',
    karmaTiers: 1000,
    karmaTierSwipeLimits: 60,
    coinReward: 250,
  },
  {
    level: 7,
    crystal: null,
    karmaTiers: 1250,
    karmaTierSwipeLimits: 60,
    coinReward: 250,
  },
  {
    level: 8,
    crystal: null,
    karmaTiers: 1500,
    karmaTierSwipeLimits: 60,
    coinReward: 250,
  },
  {
    level: 9,
    crystal: null,
    karmaTiers: 1750,
    karmaTierSwipeLimits: 60,
    coinReward: 250,
  },
  {
    level: 10,
    crystal: 'amethyst',
    karmaTiers: 2000,
    karmaTierSwipeLimits: 65,
    coinReward: 500,
  },
  {
    level: 11,
    crystal: null,
    karmaTiers: 2500,
    karmaTierSwipeLimits: 65,
    coinReward: 500,
  },
  {
    level: 12,
    crystal: null,
    karmaTiers: 3000,
    karmaTierSwipeLimits: 65,
    coinReward: 500,
  },
  {
    level: 13,
    crystal: null,
    karmaTiers: 3500,
    karmaTierSwipeLimits: 65,
    coinReward: 500,
  },
  {
    level: 14,
    crystal: null,
    karmaTiers: 4000,
    karmaTierSwipeLimits: 65,
    coinReward: 500,
  },
  {
    level: 15,
    crystal: null,
    karmaTiers: 4500,
    karmaTierSwipeLimits: 65,
    coinReward: 500,
  },
  {
    level: 16,
    crystal: null,
    karmaTiers: 5000,
    karmaTierSwipeLimits: 65,
    coinReward: 500,
  },
  {
    level: 17,
    crystal: null,
    karmaTiers: 5500,
    karmaTierSwipeLimits: 65,
    coinReward: 500,
  },
  {
    level: 18,
    crystal: null,
    karmaTiers: 6000,
    karmaTierSwipeLimits: 65,
    coinReward: 500,
  },
  {
    level: 19,
    crystal: null,
    karmaTiers: 6500,
    karmaTierSwipeLimits: 65,
    coinReward: 500,
  },
  {
    level: 20,
    crystal: 'citrine',
    karmaTiers: 7000,
    karmaTierSwipeLimits: 70,
    coinReward: 500,
  },
  {
    level: 21,
    crystal: null,
    karmaTiers: 7500,
    karmaTierSwipeLimits: 70,
    coinReward: 500,
  },
  {
    level: 22,
    crystal: null,
    karmaTiers: 8000,
    karmaTierSwipeLimits: 70,
    coinReward: 500,
  },
  {
    level: 23,
    crystal: null,
    karmaTiers: 8500,
    karmaTierSwipeLimits: 70,
    coinReward: 500,
  },
  {
    level: 24,
    crystal: null,
    karmaTiers: 9000,
    karmaTierSwipeLimits: 70,
    coinReward: 500,
  },
  {
    level: 25,
    crystal: null,
    karmaTiers: 9500,
    karmaTierSwipeLimits: 70,
    coinReward: 500,
  },
  {
    level: 26,
    crystal: null,
    karmaTiers: 10000,
    karmaTierSwipeLimits: 70,
    coinReward: 500,
  },
  {
    level: 27,
    crystal: null,
    karmaTiers: 10500,
    karmaTierSwipeLimits: 70,
    coinReward: 500,
  },
  {
    level: 28,
    crystal: null,
    karmaTiers: 11000,
    karmaTierSwipeLimits: 70,
    coinReward: 500,
  },
  {
    level: 29,
    crystal: null,
    karmaTiers: 11500,
    karmaTierSwipeLimits: 70,
    coinReward: 500,
  },
  {
    level: 30,
    crystal: 'rose',
    karmaTiers: 12000,
    karmaTierSwipeLimits: 75,
    coinReward: 500,
  },
  {
    level: 31,
    crystal: null,
    karmaTiers: 12500,
    karmaTierSwipeLimits: 75,
    coinReward: 500,
  },
  {
    level: 32,
    crystal: null,
    karmaTiers: 13000,
    karmaTierSwipeLimits: 75,
    coinReward: 500,
  },
  {
    level: 33,
    crystal: null,
    karmaTiers: 13500,
    karmaTierSwipeLimits: 75,
    coinReward: 500,
  },
  {
    level: 34,
    crystal: null,
    karmaTiers: 14000,
    karmaTierSwipeLimits: 75,
    coinReward: 500,
  },
  {
    level: 35,
    crystal: null,
    karmaTiers: 14500,
    karmaTierSwipeLimits: 75,
    coinReward: 500,
  },
  {
    level: 36,
    crystal: null,
    karmaTiers: 15000,
    karmaTierSwipeLimits: 75,
    coinReward: 500,
  },
  {
    level: 37,
    crystal: null,
    karmaTiers: 15500,
    karmaTierSwipeLimits: 75,
    coinReward: 500,
  },
  {
    level: 38,
    crystal: null,
    karmaTiers: 16000,
    karmaTierSwipeLimits: 75,
    coinReward: 500,
  },
  {
    level: 39,
    crystal: null,
    karmaTiers: 16500,
    karmaTierSwipeLimits: 75,
    coinReward: 500,
  },
  {
    level: 40,
    crystal: 'obsidian',
    karmaTiers: 17000,
    karmaTierSwipeLimits: 85,
    coinReward: 500,
  },
  {
    level: 41,
    crystal: null,
    karmaTiers: 17500,
    karmaTierSwipeLimits: 85,
    coinReward: 500,
  },
  {
    level: 42,
    crystal: null,
    karmaTiers: 18000,
    karmaTierSwipeLimits: 85,
    coinReward: 500,
  },
  {
    level: 43,
    crystal: null,
    karmaTiers: 18500,
    karmaTierSwipeLimits: 85,
    coinReward: 500,
  },
  {
    level: 44,
    crystal: null,
    karmaTiers: 19000,
    karmaTierSwipeLimits: 85,
    coinReward: 500,
  },
  {
    level: 45,
    crystal: null,
    karmaTiers: 19500,
    karmaTierSwipeLimits: 85,
    coinReward: 500,
  },
  {
    level: 46,
    crystal: null,
    karmaTiers: 20000,
    karmaTierSwipeLimits: 85,
    coinReward: 500,
  },
  {
    level: 47,
    crystal: null,
    karmaTiers: 20500,
    karmaTierSwipeLimits: 85,
    coinReward: 500,
  },
  {
    level: 48,
    crystal: null,
    karmaTiers: 21000,
    karmaTierSwipeLimits: 85,
    coinReward: 500,
  },
  {
    level: 49,
    crystal: null,
    karmaTiers: 21500,
    karmaTierSwipeLimits: 85,
    coinReward: 500,
  },
  {
    level: 50,
    crystal: 'diamond',
    karmaTiers: 22000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 51,
    crystal: null,
    karmaTiers: 22500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 52,
    crystal: null,
    karmaTiers: 23000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 53,
    crystal: null,
    karmaTiers: 23500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 54,
    crystal: null,
    karmaTiers: 24000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 55,
    crystal: null,
    karmaTiers: 24500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 56,
    crystal: null,
    karmaTiers: 25000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 57,
    crystal: null,
    karmaTiers: 25500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 58,
    crystal: null,
    karmaTiers: 26000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 59,
    crystal: null,
    karmaTiers: 26500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 60,
    crystal: null,
    karmaTiers: 27000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 61,
    crystal: null,
    karmaTiers: 27500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 62,
    crystal: null,
    karmaTiers: 28000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 63,
    crystal: null,
    karmaTiers: 28500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 64,
    crystal: null,
    karmaTiers: 29000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 65,
    crystal: null,
    karmaTiers: 29500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 66,
    crystal: null,
    karmaTiers: 30000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 67,
    crystal: null,
    karmaTiers: 30500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 68,
    crystal: null,
    karmaTiers: 31000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 69,
    crystal: null,
    karmaTiers: 31500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 70,
    crystal: null,
    karmaTiers: 32000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 71,
    crystal: null,
    karmaTiers: 32500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 72,
    crystal: null,
    karmaTiers: 33000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 73,
    crystal: null,
    karmaTiers: 33500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 74,
    crystal: null,
    karmaTiers: 34000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 75,
    crystal: null,
    karmaTiers: 34500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 76,
    crystal: null,
    karmaTiers: 35000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 77,
    crystal: null,
    karmaTiers: 35500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 78,
    crystal: null,
    karmaTiers: 36000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 79,
    crystal: null,
    karmaTiers: 36500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 80,
    crystal: null,
    karmaTiers: 37000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 81,
    crystal: null,
    karmaTiers: 37500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 82,
    crystal: null,
    karmaTiers: 38000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 83,
    crystal: null,
    karmaTiers: 38500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 84,
    crystal: null,
    karmaTiers: 39000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 85,
    crystal: null,
    karmaTiers: 39500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 86,
    crystal: null,
    karmaTiers: 40000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 87,
    crystal: null,
    karmaTiers: 40500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 88,
    crystal: null,
    karmaTiers: 41000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 89,
    crystal: null,
    karmaTiers: 41500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 90,
    crystal: null,
    karmaTiers: 42000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 91,
    crystal: null,
    karmaTiers: 42500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 92,
    crystal: null,
    karmaTiers: 43000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 93,
    crystal: null,
    karmaTiers: 43500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 94,
    crystal: null,
    karmaTiers: 44000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 95,
    crystal: null,
    karmaTiers: 44500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 96,
    crystal: null,
    karmaTiers: 45000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 97,
    crystal: null,
    karmaTiers: 45500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 98,
    crystal: null,
    karmaTiers: 46000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 99,
    crystal: null,
    karmaTiers: 46500,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
  {
    level: 100,
    crystal: null,
    karmaTiers: 47000,
    karmaTierSwipeLimits: 100,
    coinReward: 500,
  },
];

const API_RATE_LIMIT = {

  BLOCK_ROUTES_ACCESS_IF_LIMIT_REACHED : false,

  TOTAL_ROUTE_LIMIT: process.env.NODE_ENV === 'test_limiter' ? 500 : 600,
  TOTAL_ROUTE_LIMIT_BLOCK_SESSION_TIME_FOR_LIMIT: process.env.NODE_ENV === 'test_limiter' ? 0.5 : 1, //in minutes
  TOTAL_ROUTE_LIMIT_BLOCK_TIME_AFTER_LIMIT: process.env.NODE_ENV === 'test_limiter' ? 1 : 15, //in minutes

  GLOBAL_PER_ROUTE_LIMIT: process.env.NODE_ENV === 'test_limiter' ? 100 : 120,
  GLOBAL_PER_ROUTE_BLOCK_SESSION_TIME_FOR_LIMIT: process.env.NODE_ENV === 'test_limiter' ? 0.5 : 1, //in minutes
  GLOBAL_PER_ROUTE_BLOCK_TIME_AFTER_LIMIT: process.env.NODE_ENV === 'test_limiter' ? 1 : 15, //in minutes

  DAILY_PROFILE_ROUTE_LIMIT: process.env.NODE_ENV === 'test_limiter' ? 50 : 15,
  DAILY_PROFILE_BLOCK_SESSION_TIME_FOR_LIMIT: process.env.NODE_ENV === 'test_limiter' ? 0.5 : 1, //in minutes
  DAILY_PROFILE_BLOCK_TIME_AFTER_LIMIT: process.env.NODE_ENV === 'test_limiter' ? 1 : 15, //in minutes
}

function getKarmaTiers() {
  return KARMA_TIERS;
}

function getNewKarmaTiers() {
  return KARMA_CONFIG.map((val) => val.karmaTiers);
}

function getKarmaTierSwipeLimits() {
  return KARMA_TIER_SWIPE_LIMITS;
}

function getNewKarmaTierSwipeLimits() {
  return KARMA_CONFIG.map((val) => val.karmaTierSwipeLimits);
}

function getKarmaTierGhostMeter() {
  return KARMA_TIER_GHOST_METER;
}

function getInterestPageSize() {
  return 50;
}

function getDailyPostLimit() {
  return 10;
}

function getReplacedInterests() {
  return [
    ['make-up', 'makeup'],
    ['r&b', 'rnb'],
    ['lgbtq+ally', 'lgbtqally'],
  ];
}

function getNewCrystalKarmaTiers(crystal) {
  const karmaConfig = KARMA_CONFIG.find((val) => val.crystal === crystal);
  return karmaConfig ? karmaConfig.karmaTiers : undefined;
}

function getNewKarmaCrystals() {
  return KARMA_CONFIG.map((val) => val.crystal);
}

function getNewKarmaLevels() {
  return KARMA_CONFIG.map((val) => val.level);
}

function getNewKarmaCoinRewards() {
  return KARMA_CONFIG.map((val) => val.coinReward);
}

function getLocalDensityThreshold() {
  return 1000;
}

function getPopularLanguageDailyPostsThreshold() {
  return 50;
}

function getVerifyProfilePageSize() {
  return 10;
}

function getSitemapPaginationPageSize(text) {
  if (process.env.NODE_ENV === 'test') {
    return 2;
  }
  if(text == 'profiles') {
    return 500; // For prod and beta
  }
  return 100; // For prod and beta
}

function getTermsDate() {
  return new Date('2023-10-01');
}

function get_app_413_date() {
  return new Date('2024-08-31');
}

function getBackfillChatPerUserStateCutoffDate() {
  return new Date('2025-04-14');
}

function getBackfillNumYourTurnChatsCutoffDate() {
  return new Date('2025-07-02');
}

function getProfilesDetailPageProfilesCacheCutoffDate() {
  return new Date('2025-07-22');
}

function newConnectionsCountStartCutOffDate() {
  return new Date('2025-10-08');
}

function enforceVerification() {
  return true;
}

function updateLastSeenOnEveryRequest() {
  return true;
}

function throttleScoreUpdates() {
  return true;
}

function runPreemptiveModeration() {
  return true;
}

function requireManualVerificationForWeb() {
  return true;
}

function hideUnverifiedUsers() {
  return true;
}

function disableYoti() {
  return false;
}

function disableNonYotiVerification() {
  return true;
}

function disablePersonalityDatabaseFeatures() {
  return false;

  /*
  if(process.env.NODE_ENV === 'prod') {
    return true; // temporarily disable until personality database gets stable
  } else {
    return false; // always false for test, beta
  }
  */
}

function getS3BucketResized() {
  return process.env.AWS_S3_BUCKET_RESIZED;
}

const knownEmailDomains = [
  'aol.com',
  'gmail.com',
  'gmx.de',
  'googlemail.com',
  'hotmail.com',
  'icloud.com',
  'mail.ru',
  'outlook.com',
  'outlook.es',
  'privaterelay.appleid.com',
  'protonmail.com',
  'yahoo.com',
  'yahoo.com.br',
  'yahoo.de',
  'yahoo.fr',
  'yahoo.gr',
  'yahoo.it',
  'yandex.ru',
  'ymail.com',
  'abv.bg',
  'bk.ru',
  'freemail.hu',
  'hotmail.it',
  'inbox.ru',
  'interia.pl',
  'live.com',
  'o2.pl',
  'onet.pl',
  'proton.me',
  'qq.com',
  'seznam.cz',
  'web.de',
  'wp.pl',
  'gmx.de',
];

const conversionEvent = Object.freeze({
  SIGNUP: 'signup',
  PURCHASE: 'purchase',
});

const getEmbedConfig = () => {
  const replicateModelType = process.env.REPLICATE_MODEL_TYPE || 'custom';
  const customDeploymentName = process.env.REPLICATE_DEPLOYMENT_NAME || 'aifilter-embedding-deployment';

  if (replicateModelType === 'custom') {
    return {
      maxConcurrent: 80,
      deployment: `deployments/boo-world/${customDeploymentName}/predictions`,
      version: null,
    };
  }

  return {
    maxConcurrent: 600,
    deployment: null,
    version: '1c0371070cb827ec3c7f2f28adcdde54b50dcd239aa6faea0bc98b174ef03fb4',
  };
};

module.exports = {
  countryCodes,

  IMMEDIATE_DELETION_ID,
  WEB_DOMAIN,
  IMAGE_DOMAIN,
  maxDistanceFilter: 250,
  DEEPGRAM_API_KEY,
  YOTI_URL,
  YOTI_LIVENESS_ENDPOINT,
  YOTI_PEM_PATH,
  YOTI_SDK_ID,

  // Javascript strings are 2 bytes per character.
  // Max fcm payload size is 4096 bytes.
  maxNotificationBodyLength: 250,

  DAILY_PROFILE_LIMIT,
  DAILY_USER_WHO_LIKED_PROFILE_LIMIT,
  DAILY_USER_BOOST_USING_PROFILE_LIMIT,
  getDailyProfileLimit,
  DAILY_LIKES_LIMIT,
  REPORTS_UNTIL_DELETION,
  pageSize,
  getPageSize,
  instantMatchDelayMs,
  instantMatchCooldownMs,

  getKarmaTiers,
  getKarmaTierSwipeLimits,
  getKarmaTierGhostMeter,

  getInterestPageSize,
  getDailyPostLimit,

  validOs,
  knownEmailDomains,

  chatExpirationHours: 72,
  getReplacedInterests,
  getNewKarmaTiers,
  getNewKarmaTierSwipeLimits,
  getNewCrystalKarmaTiers,
  getNewKarmaCrystals,
  getNewKarmaLevels,
  getNewKarmaCoinRewards,
  karmaCrystalCoinConfig: KARMA_CONFIG,
  PROFILE_COMMENT_KARMA_REWARD: 1,
  getLocalDensityThreshold,
  rejectionReasons,
  hideFromNearbyDistance: 10,
  getPopularLanguageDailyPostsThreshold,
  getVerifyProfilePageSize,
  getTermsDate,
  enforceVerification,
  updateLastSeenOnEveryRequest,
  conversionEvent,
  get_app_413_date,
  API_RATE_LIMIT,
  throttleScoreUpdates,
  runPreemptiveModeration,
  requireManualVerificationForWeb,
  getBackfillChatPerUserStateCutoffDate,
  getBackfillNumYourTurnChatsCutoffDate,
  getProfilesDetailPageProfilesCacheCutoffDate,
  newConnectionsCountStartCutOffDate,
  hideUnverifiedUsers,
  getEmbedConfig,
  disableYoti,
  disableNonYotiVerification,
  getSitemapPaginationPageSize,
  disablePersonalityDatabaseFeatures,
  getS3BucketResized,
};
